/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.keyguard.logging

import com.android.systemui.log.LogBuffer
import com.android.systemui.log.core.LogLevel
import com.android.systemui.log.dagger.ScrimLog
import com.google.errorprone.annotations.CompileTimeConstant
import javax.inject.Inject

/**
 * A logger to log scrim state.
 *
 * To enable logcat echoing for this buffer use this command:
 * ```
 * $ adb shell cmd statusbar echo -b ScrimLog:VERBOSE
 * ```
 */
class ScrimLogger
@Inject
constructor(
    @ScrimLog val buffer: LogBuffer,
) {
    companion object {
        val TAG = ScrimLogger::class.simpleName!!
    }

    fun d(
        tag: String,
        @CompileTimeConstant msg: String,
        arg: Any,
    ) = log("$tag::$TAG", LogLevel.DEBUG, msg, arg)

    fun log(
        tag: String,
        level: LogLevel,
        @CompileTimeConstant msg: String,
        arg: Any,
    ) =
        buffer.log(
            tag,
            level,
            {
                str1 = msg
                str2 = arg.toString()
            },
            { "$str1: $str2" }
        )
}
