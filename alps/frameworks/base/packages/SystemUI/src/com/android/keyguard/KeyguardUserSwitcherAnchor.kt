/*
 * Copyright (C) 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.android.keyguard

import android.content.Context
import android.util.AttributeSet
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.LinearLayout
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat
import com.android.systemui.res.R

/**
 * Custom View for the multi-user switcher pull-down menu anchor
 */
class KeyguardUserSwitcherAnchor @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    override fun createAccessibilityNodeInfo(): AccessibilityNodeInfo {
        val info = super.createAccessibilityNodeInfo()
        AccessibilityNodeInfoCompat.wrap(info).roleDescription =
                context.getString(R.string.accessibility_multi_user_list_switcher)
        return info
    }
}
