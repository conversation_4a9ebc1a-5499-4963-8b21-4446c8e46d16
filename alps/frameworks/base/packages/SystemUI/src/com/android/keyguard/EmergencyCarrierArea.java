/*
 * Copyright (C) 2013 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.keyguard;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.android.systemui.res.R;

//SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 begin
import java.util.Arrays;
import java.util.List;
import android.ecid.EcidConfigManager;
import android.os.PersistableBundle;
//SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 end

public class EmergencyCarrierArea extends AlphaOptimizedLinearLayout {

    private CarrierText mCarrierText;
    private EmergencyButton mEmergencyButton;

    public EmergencyCarrierArea(Context context) {
        super(context);
    }

    public EmergencyCarrierArea(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        //SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 begin
        if (isDisabledEmergency()) {
            //Added by xujiaxin for [DAHLIA-2627][Defect] modify Tigo Guatemala Simlock dismiss button is not centered when no ecc button begin 2025-1-17
            setVisibility(View.GONE);
            //Added by xujiaxin for [DAHLIA-2627][Defect] modify Tigo Guatemala Simlock dismiss button is not centered when no ecc button end 2025-1-17
        }
        //SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 end
        mCarrierText = findViewById(R.id.carrier_text);
        mEmergencyButton = findViewById(R.id.emergency_call_button);

        // The emergency button overlaps the carrier text, only noticeable when highlighted.
        // So temporarily hide the carrier text while the emergency button is pressed.
        mEmergencyButton.setOnTouchListener(new OnTouchListener(){
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (mCarrierText.getVisibility() != View.VISIBLE) return false;
                switch(event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        mCarrierText.animate().alpha(0);
                        break;
                    case MotionEvent.ACTION_UP:
                        mCarrierText.animate().alpha(1);
                        break;
                }
                return false;
            }});
    }

    //SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 begin
    private boolean isDisabledEmergency() {
        EcidConfigManager ecidConfigManager = EcidConfigManager.getInstance(getContext());
        PersistableBundle persistableBundle = ecidConfigManager.getEcidConfig();
        return persistableBundle.getBoolean(EcidConfigManager.KEY_ECID_CONFIG_DISABLED_EMERGENCY_OPTION_ON_DEVICE_LOCKED, false);
    }
    //SR:MMI modify for DAHLIA-340 Lock Screen by zhanghuaibo 2024.11.25 end

    public void setCarrierTextVisible(boolean visible) {
        mCarrierText.setVisibility(visible ? View.VISIBLE : View.GONE);
    }
}
