/* This file has been modified by Unisoc (Shanghai) Technologies Co., Ltd in 2024. */
/*
 * Copyright (C) 2012 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.keyguard;

import android.content.Context;
import android.content.res.Configuration;
import android.util.AttributeSet;

import com.android.keyguard.KeyguardSecurityModel.SecurityMode;
import com.android.systemui.res.R;

/**
 * Displays a PIN pad for unlocking.
 */
public class KeyguardSimPinView extends KeyguardSimInputView {
    public static final String TAG = "KeyguardSimPinView";

    public KeyguardSimPinView(Context context) {
        this(context, null);
    }

    public KeyguardSimPinView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        resetState();
    }

    @Override
    protected int getPromptReasonStringRes(int reason) {
        // No message on SIM Pin
        return 0;
    }

    @Override
    protected int getPasswordTextViewId() {
        return R.id.simPinEntry;
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        /* AOSP Code @{
        if (mEcaView instanceof EmergencyCarrierArea) {
            ((EmergencyCarrierArea) mEcaView).setCarrierTextVisible(true);
        }
         * @}
         * Unisoc: Bug1877799 set carrier text gone for sim pin view @{ */
        IccLockViewController.getInstance(getContext()).setCarrierTextVisible(
                (EmergencyCarrierArea)mEcaView, SecurityMode.SimPin, false, true);
        /* @} */

        //modify for DAHLIA-4810 NW status should be correctly shown by tanghao 2025.24.24 begin
        mHintText = findViewById(R.id.keyguard_network_hint_text);
        if (SrHintTextUtils.INSTANCE.showNetworkStateByEcidEnable(getContext())){
            mHintText.setVisibility(VISIBLE);
        }
        //modify for DAHLIA-4810 NW status should be correctly shown by tanghao 2025.24.24 end

    }


    @Override
    public void startAppearAnimation() {
        // noop.
    }

    @Override
    public CharSequence getTitle() {
        return getContext().getString(
                com.android.internal.R.string.keyguard_accessibility_sim_pin_unlock);
    }
}
